"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { TeamContextState, TeamMember } from './types';
import { mockTeamMembers } from './mockData';

interface TeamContextActions {
  getMemberById: (id: string) => TeamMember | undefined;
  refreshMembers: () => Promise<void>;
  updateMemberStatus: (id: string, status: 'active' | 'inactive') => void;
  selectMember: (member: TeamMember | null) => void;
}

type TeamContextType = TeamContextState & TeamContextActions & {
  selectedMember: TeamMember | null;
};

const TeamContext = createContext<TeamContextType | undefined>(undefined);

interface TeamProviderProps {
  children: ReactNode;
}

export const TeamProvider: React.FC<TeamProviderProps> = ({ children }) => {
  const [state, setState] = useState<TeamContextState>({
    members: [],
    loading: true,
    error: null,
  });
  
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);

  // Simulate API call to fetch team members
  const fetchTeamMembers = async (): Promise<TeamMember[]> => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 400));
    
    // In a real implementation, this would be an API call
    // return await api.getTeamMembers();
    return mockTeamMembers;
  };

  const loadMembers = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const members = await fetchTeamMembers();
      setState(prev => ({
        ...prev,
        members,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load team members',
      }));
    }
  };

  const getMemberById = (id: string): TeamMember | undefined => {
    return state.members.find(member => member.id === id);
  };

  const refreshMembers = async (): Promise<void> => {
    await loadMembers();
  };

  const updateMemberStatus = (id: string, status: 'active' | 'inactive') => {
    setState(prev => ({
      ...prev,
      members: prev.members.map(member =>
        member.id === id ? { ...member, status } : member
      ),
    }));
  };

  const selectMember = (member: TeamMember | null) => {
    setSelectedMember(member);
  };

  // Load initial team data
  useEffect(() => {
    loadMembers();
  }, []);

  const contextValue: TeamContextType = {
    ...state,
    selectedMember,
    getMemberById,
    refreshMembers,
    updateMemberStatus,
    selectMember,
  };

  return (
    <TeamContext.Provider value={contextValue}>
      {children}
    </TeamContext.Provider>
  );
};

export const useTeam = (): TeamContextType => {
  const context = useContext(TeamContext);
  if (context === undefined) {
    throw new Error('useTeam must be used within a TeamProvider');
  }
  return context;
};
