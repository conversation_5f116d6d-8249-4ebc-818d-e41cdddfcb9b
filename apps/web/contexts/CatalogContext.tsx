"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { CatalogContextState, CatalogItem } from './types';
import { mockCatalogItems } from './mockData';

interface CatalogContextActions {
  selectItem: (item: CatalogItem | null) => void;
  getItemById: (id: string) => CatalogItem | undefined;
  refreshItems: () => Promise<void>;
}

type CatalogContextType = CatalogContextState & CatalogContextActions;

const CatalogContext = createContext<CatalogContextType | undefined>(undefined);

interface CatalogProviderProps {
  children: ReactNode;
}

export const CatalogProvider: React.FC<CatalogProviderProps> = ({ children }) => {
  const [state, setState] = useState<CatalogContextState>({
    items: [],
    selectedItem: null,
    loading: true,
    error: null,
  });

  // Simulate API call to fetch catalog items
  const fetchCatalogItems = async (): Promise<CatalogItem[]> => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // In a real implementation, this would be an API call
    // return await api.getCatalogItems();
    return mockCatalogItems;
  };

  const refreshItems = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const items = await fetchCatalogItems();
      setState(prev => ({
        ...prev,
        items,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch catalog items',
      }));
    }
  };

  const selectItem = (item: CatalogItem | null) => {
    setState(prev => ({
      ...prev,
      selectedItem: item,
    }));
  };

  const getItemById = (id: string): CatalogItem | undefined => {
    return state.items.find(item => item.id === id);
  };

  // Load initial data
  useEffect(() => {
    refreshItems();
  }, []);

  const contextValue: CatalogContextType = {
    ...state,
    selectItem,
    getItemById,
    refreshItems,
  };

  return (
    <CatalogContext.Provider value={contextValue}>
      {children}
    </CatalogContext.Provider>
  );
};

export const useCatalog = (): CatalogContextType => {
  const context = useContext(CatalogContext);
  if (context === undefined) {
    throw new Error('useCatalog must be used within a CatalogProvider');
  }
  return context;
};
