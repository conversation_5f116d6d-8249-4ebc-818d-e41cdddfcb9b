// Types for the application data structures

// Business types that determine UI layout
export type BusinessType =
  | 'fabrication'
  | 'retail'
  | 'services'
  | 'manufacturing'
  | 'distribution'
  | 'construction';

// User and business context
export interface User {
  id: string;
  name: string;
  email: string;
  businessType: BusinessType;
  companyName: string;
}

export interface CatalogItem {
  id: string;
  productName: string;
  productDescription: string;
  categoryLabel: 'Product' | 'Service';
  imageSrc?: string;
  details?: CatalogItemDetails;
}

export interface CatalogItemDetails {
  title: string;
  type: 'Product' | 'Service';
  // Product-specific fields
  fabrication?: string;
  materials?: Material[];
  // Service-specific fields
  duration?: string;
  deliverables?: Deliverable[];
  requirements?: string[];
  serviceType?: string; // References service template (e.g., 'laser_cutting', 'welding_service')
  serviceSpecifications?: Record<string, any>; // Dynamic fields based on service template
  // Common fields
  documents: Document[];
  images: string[];
}

export interface Deliverable {
  id: string;
  name: string;
  description: string;
  timeline: string;
}

export interface Material {
  id: string;
  name: string;
  material: string;
  quantity: number;
  unit: string;
}

export interface Document {
  id: string;
  name: string;
  url: string;
  type: string;
}

export interface InventoryItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  unit: string;
  category: string;
  categoryId: string; // References inventory category from templates
  minStock?: number;
  maxStock?: number;
  cost?: number;
  location?: string;
  supplier?: string;
  lastUpdated: string;
  // Dynamic fields based on category
  additionalFields?: Record<string, any>;
}

export interface FinancialRecord {
  id: string;
  type: 'income' | 'expense';
  amount: number;
  description: string;
  date: string;
  category: string;
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  email: string;
  avatar?: string;
  status: 'active' | 'inactive';
  salary: number;
  currency: string;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  status: 'planning' | 'in-progress' | 'completed' | 'on-hold';
  startDate: string;
  endDate?: string;
  teamMembers: string[]; // Team member IDs
}

// Context state interfaces
export interface CatalogContextState {
  items: CatalogItem[];
  selectedItem: CatalogItem | null;
  loading: boolean;
  error: string | null;
}

export interface InventoryContextState {
  items: InventoryItem[];
  loading: boolean;
  error: string | null;
}

export interface FinancialContextState {
  records: FinancialRecord[];
  loading: boolean;
  error: string | null;
}

export interface TeamContextState {
  members: TeamMember[];
  loading: boolean;
  error: string | null;
}

export interface ProjectContextState {
  projects: Project[];
  loading: boolean;
  error: string | null;
}
