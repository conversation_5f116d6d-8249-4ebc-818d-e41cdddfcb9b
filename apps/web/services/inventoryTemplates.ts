/**
 * Inventory Templates for Different Business Types
 * 
 * This module provides inventory category templates based on business type.
 * Each business type has specific inventory categories that are relevant to their operations.
 * These templates will be used to organize inventory items and provide context-specific views.
 */

import { BusinessType } from '../contexts/types';

export interface InventoryCategory {
  id: string;
  display_name: string;
  description: string;
  icon?: string;
  typical_items: string[];
  tracking_fields: string[]; // Additional fields to track for this category
}

export interface BusinessInventoryTemplate {
  business_type: BusinessType;
  display_name: string;
  categories: InventoryCategory[];
}

/**
 * Inventory Templates Dictionary
 * Maps business types to their relevant inventory categories
 */
export const inventoryTemplates: Record<BusinessType, BusinessInventoryTemplate> = {
  fabrication: {
    business_type: 'fabrication',
    display_name: 'Fabricación',
    categories: [
      {
        id: 'raw_materials',
        display_name: 'Materia Prima',
        description: 'Materiales base para fabricación',
        icon: 'inventory_2',
        typical_items: ['Láminas de acero', 'Barras de aluminio', 'Tubos', 'Perfiles'],
        tracking_fields: ['material_grade', 'dimensions', 'supplier', 'lot_number']
      },

      {
        id: 'finished_products',
        display_name: 'Productos Terminados',
        description: 'Productos completamente fabricados listos para entrega',
        icon: 'check_circle',
        typical_items: ['Estructuras metálicas', 'Maquinaria', 'Componentes terminados'],
        tracking_fields: ['quality_status', 'customer', 'delivery_date', 'warranty_period']
      },
      {
        id: 'consumables',
        display_name: 'Consumibles',
        description: 'Materiales que se consumen durante el proceso',
        icon: 'local_fire_department',
        typical_items: ['Electrodos', 'Gases', 'Discos de corte', 'Lubricantes'],
        tracking_fields: ['expiry_date', 'usage_rate', 'reorder_point', 'safety_stock']
      },
      {
        id: 'tools_equipment',
        display_name: 'Herramientas y Equipos',
        description: 'Herramientas y equipos de fabricación',
        icon: 'handyman',
        typical_items: ['Soldadoras', 'Cortadoras', 'Taladros', 'Herramientas manuales'],
        tracking_fields: ['condition', 'maintenance_date', 'calibration_date', 'location']
      }
    ]
  },

  retail: {
    business_type: 'retail',
    display_name: 'Retail/Ventas',
    categories: [
      {
        id: 'merchandise',
        display_name: 'Mercancía',
        description: 'Productos para venta directa',
        icon: 'shopping_cart',
        typical_items: ['Productos terminados', 'Artículos de temporada', 'Promociones'],
        tracking_fields: ['sku', 'barcode', 'price', 'margin', 'supplier']
      },
      {
        id: 'seasonal',
        display_name: 'Temporada',
        description: 'Productos estacionales',
        icon: 'calendar_month',
        typical_items: ['Artículos navideños', 'Ropa de temporada', 'Decoraciones'],
        tracking_fields: ['season', 'markdown_date', 'clearance_price', 'storage_location']
      },
      {
        id: 'promotional',
        display_name: 'Promocional',
        description: 'Artículos en promoción o descuento',
        icon: 'local_offer',
        typical_items: ['Ofertas especiales', 'Liquidaciones', 'Combos'],
        tracking_fields: ['original_price', 'discount_percentage', 'promotion_end_date', 'min_quantity']
      }
    ]
  },

  services: {
    business_type: 'services',
    display_name: 'Servicios',
    categories: [
      {
        id: 'equipment',
        display_name: 'Equipos',
        description: 'Equipos para prestación de servicios',
        icon: 'devices',
        typical_items: ['Computadoras', 'Software', 'Herramientas especializadas'],
        tracking_fields: ['serial_number', 'warranty_date', 'assigned_user', 'maintenance_schedule']
      },
      {
        id: 'consumables',
        display_name: 'Consumibles',
        description: 'Materiales que se consumen en la prestación del servicio',
        icon: 'local_fire_department',
        typical_items: ['Materiales de consultoría', 'Muestras', 'Material promocional'],
        tracking_fields: ['project', 'client', 'billable', 'expiry_date']
      }
    ]
  },

  manufacturing: {
    business_type: 'manufacturing',
    display_name: 'Manufactura',
    categories: [
      {
        id: 'raw_materials',
        display_name: 'Materia Prima',
        description: 'Materiales base para manufactura',
        icon: 'inventory_2',
        typical_items: ['Componentes', 'Materiales base', 'Insumos químicos'],
        tracking_fields: ['batch_number', 'quality_grade', 'supplier_certification', 'storage_conditions']
      },
      {
        id: 'components',
        display_name: 'Componentes',
        description: 'Partes y componentes para ensamble',
        icon: 'precision_manufacturing',
        typical_items: ['Circuitos', 'Motores', 'Sensores', 'Conectores'],
        tracking_fields: ['part_number', 'revision', 'supplier', 'lead_time']
      },

      {
        id: 'finished_goods',
        display_name: 'Productos Terminados',
        description: 'Productos listos para distribución',
        icon: 'check_circle',
        typical_items: ['Productos empacados', 'Lotes terminados', 'Productos certificados'],
        tracking_fields: ['lot_number', 'quality_certificate', 'expiry_date', 'customer_order']
      },
      {
        id: 'packaging',
        display_name: 'Empaque',
        description: 'Materiales de empaque y etiquetado',
        icon: 'package_2',
        typical_items: ['Cajas', 'Etiquetas', 'Material de protección'],
        tracking_fields: ['size', 'material_type', 'print_specification', 'environmental_rating']
      },
      {
        id: 'maintenance',
        display_name: 'Mantenimiento',
        description: 'Repuestos y materiales de mantenimiento',
        icon: 'build',
        typical_items: ['Repuestos', 'Lubricantes', 'Filtros', 'Herramientas'],
        tracking_fields: ['equipment_compatibility', 'maintenance_schedule', 'criticality', 'vendor']
      }
    ]
  },

  distribution: {
    business_type: 'distribution',
    display_name: 'Distribución',
    categories: [
      {
        id: 'incoming_goods',
        display_name: 'Mercancía Entrante',
        description: 'Productos recibidos de proveedores',
        icon: 'local_shipping',
        typical_items: ['Productos recibidos', 'Mercancía en tránsito', 'Devoluciones'],
        tracking_fields: ['purchase_order', 'supplier', 'received_date', 'quality_inspection']
      },
      {
        id: 'storage',
        display_name: 'Almacenamiento',
        description: 'Productos en almacén',
        icon: 'warehouse',
        typical_items: ['Productos almacenados', 'Reservas', 'Stock disponible'],
        tracking_fields: ['location', 'zone', 'rotation_date', 'storage_conditions']
      },
      {
        id: 'outgoing_goods',
        display_name: 'Mercancía Saliente',
        description: 'Productos listos para envío',
        icon: 'outbound',
        typical_items: ['Pedidos preparados', 'Envíos programados', 'Productos en ruta'],
        tracking_fields: ['customer_order', 'shipping_date', 'carrier', 'tracking_number']
      },
      {
        id: 'packaging_materials',
        display_name: 'Material de Empaque',
        description: 'Materiales para empaque y envío',
        icon: 'package_2',
        typical_items: ['Cajas', 'Plástico burbuja', 'Etiquetas de envío'],
        tracking_fields: ['size', 'weight_capacity', 'cost_per_unit', 'supplier']
      }
    ]
  },

  construction: {
    business_type: 'construction',
    display_name: 'Construcción',
    categories: [
      {
        id: 'materials',
        display_name: 'Materiales',
        description: 'Materiales de construcción',
        icon: 'construction',
        typical_items: ['Cemento', 'Varilla', 'Blocks', 'Madera'],
        tracking_fields: ['grade', 'specifications', 'delivery_date', 'project_assignment']
      },
      {
        id: 'tools',
        display_name: 'Herramientas',
        description: 'Herramientas de construcción',
        icon: 'handyman',
        typical_items: ['Taladros', 'Sierras', 'Niveles', 'Herramientas manuales'],
        tracking_fields: ['condition', 'assigned_crew', 'maintenance_date', 'location']
      },
      {
        id: 'equipment',
        display_name: 'Equipos',
        description: 'Maquinaria y equipos pesados',
        icon: 'precision_manufacturing',
        typical_items: ['Excavadoras', 'Grúas', 'Mezcladoras', 'Generadores'],
        tracking_fields: ['hours_used', 'fuel_consumption', 'operator', 'maintenance_schedule']
      },
      {
        id: 'safety',
        display_name: 'Seguridad',
        description: 'Equipos de protección personal',
        icon: 'security',
        typical_items: ['Cascos', 'Arneses', 'Guantes', 'Botas de seguridad'],
        tracking_fields: ['certification', 'expiry_date', 'assigned_worker', 'inspection_date']
      },
      {
        id: 'consumables',
        display_name: 'Consumibles',
        description: 'Materiales consumibles',
        icon: 'local_fire_department',
        typical_items: ['Combustible', 'Lubricantes', 'Soldadura', 'Pintura'],
        tracking_fields: ['usage_rate', 'project', 'safety_data_sheet', 'storage_requirements']
      }
    ]
  }
};

/**
 * Mock fetch function to simulate API call for inventory categories
 * @param businessType - The business type to get categories for
 * @returns Promise resolving to the business inventory template
 */
export async function fetchInventoryTemplate(businessType: BusinessType): Promise<BusinessInventoryTemplate | null> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
  
  const template = inventoryTemplates[businessType];
  
  if (!template) {
    throw new Error(`Inventory template for business type '${businessType}' not found`);
  }
  
  return template;
}

/**
 * Get inventory categories for a specific business type
 * @param businessType - The business type to get categories for
 * @returns Promise resolving to array of inventory categories
 */
export async function fetchInventoryCategories(businessType: BusinessType): Promise<InventoryCategory[]> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 50));
  
  const template = inventoryTemplates[businessType];
  return template ? template.categories : [];
}

/**
 * Get all available business types with inventory templates
 * @returns Promise resolving to array of business types
 */
export async function fetchAvailableBusinessTypes(): Promise<BusinessType[]> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 50));
  
  return Object.keys(inventoryTemplates) as BusinessType[];
}
