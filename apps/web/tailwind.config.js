import { colors, radius, spacing, fontFamily, fontSize, fontWeight, lineHeight, boxShadow } from '@admin/design-tokens';

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    '../../packages/ui/src/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors,
      borderRadius: radius,
      spacing,
      fontFamily,
      fontSize,
      fontWeight,
      lineHeight,
      boxShadow,
    },
  },
  plugins: [],
};
