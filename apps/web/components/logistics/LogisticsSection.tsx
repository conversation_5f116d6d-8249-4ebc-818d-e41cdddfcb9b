import React from "react";
import { LogisticsCard } from "@admin/ui";

interface LogisticsSectionProps {
  isModalExpanded: boolean;
  setIsModalExpanded: (expanded: boolean) => void;
  modalHeaderTextColor?: string;
}

export const LogisticsSection: React.FC<LogisticsSectionProps> = ({ isModalExpanded, setIsModalExpanded, modalHeaderTextColor }) => {
  return (
    <div className="logistics-section w-full">
      <div className="logistics-section__cards grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
        <LogisticsCard
          vehicleName="Chevrolet Express"
          dateStart="05, Julio 2025"
          dateEnd="05, Julio 2025"
          status="available"
          statusText="Disponible"
          imageUrl="/images/van.png"
          className="w-full"
          imageClassName="max-h-32"
        />
        <LogisticsCard
          vehicleName="Camión 2"
          dateStart="05, Julio 2025"
          dateEnd="05, Julio 2025"
          status="en_route"
          statusText="En ruta"
          imageUrl="/images/truck.png"
          className="w-full"
          imageClassName="max-h-32"
        />
      </div>
    </div>
  );
}; 