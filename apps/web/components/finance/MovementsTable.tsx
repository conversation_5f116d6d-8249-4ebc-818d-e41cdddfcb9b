"use client";

import { Table, TableHeader, TableHeaderRow, TableHeaderCell, TableBody, TableBodyRow, TableBodyCell } from "@admin/ui";

interface Movement {
  fecha: string;
  concepto: string;
  monto: string;
  tipo: string;
  asignacion: string;
  categoria: string;
  comportamiento: string;
  comprobante: string;
}

export function MovementsTable() {
  // Sample movements data
  const movementsData: Movement[] = [
    {
      fecha: '2025-06-01',
      concepto: 'Compra de metal',
      monto: '$ 1.532,21',
      tipo: 'Salida',
      asignacion: 'Proyecto #182',
      categoria: 'Material',
      comportamiento: 'Fijo',
      comprobante: '🗂'
    },
    {
      fecha: '2025-06-01',
      concepto: 'Depósito',
      monto: '+ $ 32.385,69',
      tipo: 'Entrada',
      asignacion: 'Proyecto #182',
      categoria: 'Depósito',
      comportamiento: 'Anticipo',
      comprobante: '🗂'
    },
    {
      fecha: '2025-06-01',
      concepto: 'Compra de metal',
      monto: '$ 1.532,21',
      tipo: 'Salida',
      asignacion: 'Proyecto #182',
      categoria: 'Material',
      comportamiento: 'Fijo',
      comprobante: '🗂'
    }
  ];

  return (
    <div className="h-full">
      <Table>
        <TableHeader>
          <TableHeaderRow>
            <TableHeaderCell variant="first">Fecha</TableHeaderCell>
            <TableHeaderCell variant="middle">Concepto</TableHeaderCell>
            <TableHeaderCell variant="middle">Monto</TableHeaderCell>
            <TableHeaderCell variant="middle">Tipo</TableHeaderCell>
            <TableHeaderCell variant="middle">Asignación</TableHeaderCell>
            <TableHeaderCell variant="middle">Categoría</TableHeaderCell>
            <TableHeaderCell variant="middle">Comportamiento</TableHeaderCell>
            <TableHeaderCell variant="last">Comprobante</TableHeaderCell>
          </TableHeaderRow>
        </TableHeader>
        <TableBody>
          {movementsData.map((movement, index) => {
            const isFirst = index === 0;
            const isLast = index === movementsData.length - 1;
            const rowPosition = isFirst ? 'first' : isLast ? 'last' : 'middle';
            
            return (
              <TableBodyRow key={index}>
                <TableBodyCell variant="first" rowPosition={rowPosition}>{movement.fecha}</TableBodyCell>
                <TableBodyCell variant="middle" rowPosition={rowPosition}>{movement.concepto}</TableBodyCell>
                <TableBodyCell variant="middle" rowPosition={rowPosition}>{movement.monto}</TableBodyCell>
                <TableBodyCell variant="middle" rowPosition={rowPosition}>{movement.tipo}</TableBodyCell>
                <TableBodyCell variant="middle" rowPosition={rowPosition}>{movement.asignacion}</TableBodyCell>
                <TableBodyCell variant="middle" rowPosition={rowPosition}>{movement.categoria}</TableBodyCell>
                <TableBodyCell variant="middle" rowPosition={rowPosition}>{movement.comportamiento}</TableBodyCell>
                <TableBodyCell variant="last" rowPosition={rowPosition}>{movement.comprobante}</TableBodyCell>
              </TableBodyRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
