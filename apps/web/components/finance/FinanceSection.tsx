"use client";

import { useState } from "react";
import { FinanceCard, FinanceTabButton, Calendar } from "@admin/ui";
import { MovementsTable } from "./MovementsTable";
import { FinancialStatementsTable } from "./FinancialStatementsTable";

type FinanceTab = 'activos' | 'pasivos' | 'forecast' | 'movimientos' | 'estados-financieros';

interface FinanceSectionProps {
  isModalExpanded: boolean;
  setIsModalExpanded: (expanded: boolean) => void;
}

export function FinanceSection({ isModalExpanded, setIsModalExpanded }: FinanceSectionProps) {
  const [activeFinanceTab, setActiveFinanceTab] = useState<FinanceTab>('activos');

  return (
    <div className="h-full">
      {!isModalExpanded ? (
        // Collapsed view - Finance cards grid
        <div className="grid grid-cols-2 gap-3 h-full">
          <FinanceCard
            title="Movimientos"
            onClick={() => {
              setActiveFinanceTab('movimientos');
              setIsModalExpanded(true);
            }}
          />
          <FinanceCard
            title="Activos"
            onClick={() => {
              setActiveFinanceTab('activos');
              setIsModalExpanded(true);
            }}
          />
          <FinanceCard
            title="Pasivos"
            onClick={() => {
              setActiveFinanceTab('pasivos');
              setIsModalExpanded(true);
            }}
          />
          <FinanceCard
            title="Pronósticos"
            onClick={() => {
              setActiveFinanceTab('forecast');
              setIsModalExpanded(true);
            }}
          />
          <div className="col-span-2">
            <FinanceCard
              title="Estados Financieros"
              onClick={() => {
                setActiveFinanceTab('estados-financieros');
                setIsModalExpanded(true);
              }}
            />
          </div>
        </div>
      ) : (
        // Expanded view - Finance tabs and content
        <div className="h-full flex flex-col">
          {/* Finance Tab Buttons */}
          <div className="flex gap-2 mb-6">
            <FinanceTabButton
              title="Activos"
              isActive={activeFinanceTab === 'activos'}
              onClick={() => setActiveFinanceTab('activos')}
            />
            <FinanceTabButton
              title="Pasivos"
              isActive={activeFinanceTab === 'pasivos'}
              onClick={() => setActiveFinanceTab('pasivos')}
            />
            <FinanceTabButton
              title="Forecast"
              isActive={activeFinanceTab === 'forecast'}
              onClick={() => setActiveFinanceTab('forecast')}
            />
            <FinanceTabButton
              title="Movimientos"
              isActive={activeFinanceTab === 'movimientos'}
              onClick={() => setActiveFinanceTab('movimientos')}
            />
            <FinanceTabButton
              title="Estados Financieros"
              isActive={activeFinanceTab === 'estados-financieros'}
              onClick={() => setActiveFinanceTab('estados-financieros')}
            />
          </div>

          {/* Tab Content Container */}
          <div className="flex-1 border border-[var(--color-stroke)] rounded-[var(--radius-8)] bg-[var(--color-background-primary)] overflow-hidden flex flex-col min-h-0">
            <div className="flex-1 px-16 pt-20 pb-16 overflow-y-auto min-h-0">
              {activeFinanceTab === 'forecast' ? (
                // Calendar View for Forecast
                <Calendar
                  title="Calendario"
                  subtitle="Junio 2025"
                />
              ) : activeFinanceTab === 'movimientos' ? (
                // Movements Table
                <MovementsTable />
              ) : activeFinanceTab === 'estados-financieros' ? (
                // Financial Statements Table
                <FinancialStatementsTable />
              ) : (
                // Placeholder for other tabs
                <div className="flex items-center justify-center h-full text-[var(--color-text-secondary)]">
                  <p className="text-sm">
                    Contenido de {activeFinanceTab} - por implementar
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
