"use client";

import { CatalogItem, SearchBarSet, FilterButtonSet } from "@admin/ui";
import { useCatalog, useUser } from "../../contexts";
import { CatalogDetailFactory } from "./CatalogDetailFactory";

interface CatalogSectionProps {
  isModalExpanded: boolean;
  setIsModalExpanded: (expanded: boolean) => void;
  modalHeaderTextColor: string;
}

export function CatalogSection({ isModalExpanded, setIsModalExpanded, modalHeaderTextColor }: CatalogSectionProps) {
  const { items: catalogItems, selectedItem, selectItem, loading: catalogLoading } = useCatalog();
  const { user } = useUser();

  return (
    <div className={isModalExpanded ? "flex gap-12 h-full overflow-hidden" : ""}>
      {/* Left side - Catalog Items Grid */}
      <div className={isModalExpanded ? "w-[450px] flex-shrink-0 overflow-y-auto" : ""}>
        {/* Search and Filter Controls */}
        <div className="flex items-center gap-3 mb-[var(--spacing-8)]">
          <div className="flex-1">
            <SearchBarSet
              placeholder="Buscar"
              exampleText="Lamina de acero al carbon"
              showExample={false}
              onSearchChange={(value: string) => console.log('Searching catalog:', value)}
            />
          </div>
          <FilterButtonSet
            onFilterClick={() => console.log('Filter clicked')}
          />
        </div>

        {catalogLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-[var(--color-text-secondary)]">Cargando catálogo...</div>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-3">
            {catalogItems.map((item) => (
              <div key={item.id} onClick={() => {
                selectItem(item);
                if (!isModalExpanded) {
                  setIsModalExpanded(true);
                }
              }}>
                <CatalogItem
                  productName={item.productName}
                  productDescription={item.productDescription}
                  categoryLabel={item.categoryLabel}
                  imageSrc={item.imageSrc}
                  isActive={selectedItem?.id === item.id}
                  className="cursor-pointer hover:opacity-80 transition-opacity"
                />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Right side - Item Details (only when expanded) */}
      {isModalExpanded && (
        <div className="flex-1 border rounded-[8px] bg-[var(--color-background-primary)] overflow-hidden flex flex-col min-h-0">
          <div className="flex-1 px-16 pt-20 pb-16 overflow-y-auto min-h-0">
            {selectedItem && user ? (
              <CatalogDetailFactory
                businessType={user.businessType}
                item={selectedItem}
                modalHeaderTextColor={modalHeaderTextColor}
              />
            ) : selectedItem && !user ? (
              <div className="flex items-center justify-center h-full text-[var(--color-text-secondary)]">
                Cargando información del usuario...
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-[var(--color-text-secondary)]">
                Selecciona un producto para ver los detalles
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
