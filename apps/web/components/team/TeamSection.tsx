"use client";

import { Team<PERSON>ard, SearchBarSet, FilterDropdown, FilterPills, type FilterOption } from "@admin/ui";
import { useTeam } from "../../contexts";
import { useState } from "react";

interface TeamSectionProps {
  isModalExpanded: boolean;
  setIsModalExpanded: (expanded: boolean) => void;
  modalHeaderTextColor: string;
}

export function TeamSection({ isModalExpanded, setIsModalExpanded, modalHeaderTextColor }: TeamSectionProps) {
  const { members: teamMembers, selectedMember, selectMember, loading: teamLoading } = useTeam();
  const [selectedFilters, setSelectedFilters] = useState<FilterOption[]>([]);

  // Team-specific filter options
  const teamFilterOptions: FilterOption[] = [
    { id: 'active', label: 'Activos', value: 'active' },
    { id: 'inactive', label: 'Inactivos', value: 'inactive' },
    { id: 'supervisor', label: 'Supervisores', value: 'supervisor' },
    { id: 'engineer', label: 'Ingenieros', value: 'engineer' },
    { id: 'designer', label: 'Di<PERSON><PERSON><PERSON><PERSON>', value: 'designer' },
    { id: 'technician', label: 'Técnicos', value: 'technician' },
    { id: 'coordinator', label: 'Coordinadores', value: 'coordinator' },
    { id: 'analyst', label: 'Analistas', value: 'analyst' }
  ];

  return (
    <div className={isModalExpanded ? "flex gap-12 h-full overflow-hidden" : ""}>
      {/* Left side - Team Members Grid */}
      <div className={isModalExpanded ? "w-[450px] flex-shrink-0 overflow-y-auto" : ""}>
        {/* Search and Filter Controls */}
        <div className="mb-[var(--spacing-8)]">
          <div className="flex items-center gap-3 mb-2">
            <div className="flex-1 min-w-0">
              <SearchBarSet
                placeholder="Buscar"
                exampleText="Franco Eduardo"
                showExample={false}
                onSearchChange={(value: string) => console.log('Searching team:', value)}
              />
            </div>
            <div className="flex-shrink-0">
              <FilterDropdown
                options={teamFilterOptions}
                selectedOptions={selectedFilters}
                onOptionsChange={setSelectedFilters}
                onFilterClick={() => console.log('Filter clicked')}
              />
            </div>
          </div>

          {/* Selected Filter Pills */}
          <FilterPills
            selectedOptions={selectedFilters}
            onRemoveOption={(optionId) => {
              setSelectedFilters(prev => prev.filter(option => option.id !== optionId));
            }}
          />
        </div>

        {teamLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-[var(--color-text-secondary)]">Cargando equipo...</div>
          </div>
        ) : (
          /* Team Members Grid */
          <div className="grid grid-cols-2 gap-3">
            {teamMembers.map((member) => (
              <div key={member.id} onClick={() => {
                selectMember(member);
                if (!isModalExpanded) {
                  setIsModalExpanded(true);
                }
              }}>
                <TeamCard
                  name={member.name}
                  role={member.role}
                  avatar={member.avatar}
                  isActive={selectedMember?.id === member.id}
                  className="cursor-pointer hover:opacity-80 transition-opacity"
                />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Right side - Member Details (only when expanded) */}
      {isModalExpanded && (
        <div className="flex-1 border rounded-[8px] bg-[var(--color-background-primary)] overflow-hidden flex flex-col min-h-0">
          <div className="flex-1 px-16 pt-20 pb-16 overflow-y-auto min-h-0">
            {selectedMember ? (
              <div>
                <div className="modal-main-header">
                  <h3 className="text-xl font-semibold mb-4" style={{ color: modalHeaderTextColor }}>
                    {selectedMember.name}
                  </h3>
                  <div className="text-sm text-[var(--color-text-secondary)]">
                    {selectedMember.role}
                  </div>
                </div>

                {/* Contact Information */}
                <div className="modal-section">
                  <h4 className="modal-sub-header">
                    Información de Contacto
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <span className="text-sm">Email:</span>
                      <span className="font-medium text-blue-600">{selectedMember.email}</span>
                    </div>
                    <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <span className="text-sm">Estado:</span>
                      <span className={`font-medium ${selectedMember.status === 'active' ? 'text-green-600' : 'text-red-600'}`}>
                        {selectedMember.status === 'active' ? 'Activo' : 'Inactivo'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <span className="text-sm">Salario:</span>
                      <span className="font-medium text-green-600">
                        {new Intl.NumberFormat('en-US', {
                          style: 'currency',
                          currency: selectedMember.currency
                        }).format(selectedMember.salary)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Performance Metrics */}
                <div className="modal-section">
                  <h4 className="modal-sub-header">
                    Métricas de Rendimiento
                  </h4>
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div className="p-3 bg-blue-50 rounded">
                      <div className="text-lg font-bold text-blue-600">95%</div>
                      <div className="text-xs text-gray-600">Asistencia</div>
                    </div>
                    <div className="p-3 bg-green-50 rounded">
                      <div className="text-lg font-bold text-green-600">4.8</div>
                      <div className="text-xs text-gray-600">Evaluación</div>
                    </div>
                    <div className="p-3 bg-purple-50 rounded">
                      <div className="text-lg font-bold text-purple-600">12</div>
                      <div className="text-xs text-gray-600">Proyectos</div>
                    </div>
                  </div>
                </div>

                {/* Recent Activity */}
                <div className="modal-section">
                  <h4 className="modal-sub-header">
                    Actividad Reciente
                  </h4>
                  <div className="space-y-2">
                    <div className="p-2 bg-gray-50 rounded text-sm">
                      <div className="font-medium">Completó revisión de calidad</div>
                      <div className="text-gray-600">Hace 2 horas</div>
                    </div>
                    <div className="p-2 bg-gray-50 rounded text-sm">
                      <div className="font-medium">Actualizó reporte de producción</div>
                      <div className="text-gray-600">Ayer</div>
                    </div>
                    <div className="p-2 bg-gray-50 rounded text-sm">
                      <div className="font-medium">Participó en reunión de equipo</div>
                      <div className="text-gray-600">Hace 3 días</div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-[var(--color-text-secondary)]">
                Selecciona un miembro del equipo para ver los detalles
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
