"use client";

import * as React from "react";
import { ChevronUp } from "lucide-react";
import { cn } from "./lib/utils";

export interface MessageBarProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Message text to display */
  message?: string;
  /** Arrow icon source for the circular button */
  arrowIconSrc?: string;
  /** Callback when arrow button is clicked */
  onArrowClick?: () => void;
}

const MessageBar = React.forwardRef<HTMLDivElement, MessageBarProps>(
  ({
    className,
    message = "Pregunta lo que sea",
    arrowIconSrc,
    onArrowClick,
    ...props
  }, ref) => {
    // Component-specific variables from global design tokens
    const messageBarArrowButtonBg = 'var(--color-frame-primary)';

    return (
      <div
        ref={ref}
        className={cn(
          "w-full flex items-center justify-between bg-[var(--color-background-primary)] text-[var(--color-text-primary)] px-[var(--spacing-16)] py-[var(--spacing-12)] rounded-[var(--radius-8)]",
          className
        )}
        style={{ boxShadow: 'var(--shadow-outer1)' }}
        {...props}
      >
        {/* Left side - Message */}
        <div className="flex items-center">
          <span className="text-sm font-medium">
            {message}
          </span>
        </div>

        {/* Right side - Circular arrow button */}
        <div className="flex items-center">
          <button
            onClick={onArrowClick}
            className="w-8 h-8 rounded-full text-[var(--color-icon-inverse-icon)] flex items-center justify-center hover:opacity-80 transition-opacity"
            style={{ backgroundColor: messageBarArrowButtonBg }}
          >
            {arrowIconSrc ? (
              <img
                className="w-[18px] h-[18px]"
                width={18}
                height={18}
                alt="Arrow icon"
                src={arrowIconSrc}
              />
            ) : (
              <ChevronUp size={18} />
            )}
          </button>
        </div>
      </div>
    );
  }
);

MessageBar.displayName = "MessageBar";

export { MessageBar };
