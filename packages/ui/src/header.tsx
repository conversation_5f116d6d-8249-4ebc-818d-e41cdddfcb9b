"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface HeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Logo text to display */
  logoText?: string;
  /** Add button text */
  addButtonText?: string;
  /** Profile image source */
  profileImageSrc?: string;
  /** Add icon source */
  addIconSrc?: string;
  /** Callback when add button is clicked */
  onAddClick?: () => void;
  /** Callback when profile is clicked */
  onProfileClick?: () => void;
}

const Header = React.forwardRef<HTMLDivElement, HeaderProps>(
  ({
    className,
    logoText = "LOGO",
    addButtonText = "Añadir",
    profileImageSrc = "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
    addIconSrc,
    onAddClick,
    onProfileClick,
    ...props
  }, ref) => {
    // Default add icon as inline SVG
    const defaultAddIcon = (
      <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8.5 3V14M3 8.5H14" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
      </svg>
    );

    return (
      <div
        ref={ref}
        className={cn(
          "w-full relative overflow-hidden flex flex-row items-center justify-between py-[var(--spacing-16)] box-border gap-0 text-left text-xl text-black font-inter",
          className
        )}
        {...props}
      >
        <div className="relative font-bold">
          {logoText}
        </div>

        <div className="flex flex-row items-center justify-end gap-3 text-[11px]">
          <button
            onClick={onAddClick}
            className="w-[76px] rounded-sm border-[#bdbdbd] border-solid border-[0.5px] box-border h-[23px] flex flex-col items-center justify-center py-[3px] pl-0.5 pr-[3px] hover:bg-gray-50 transition-colors"
          >
            <div className="w-14 relative h-[17px]">
              <div className="absolute top-[2.25px] left-[21px] font-medium">
                {addButtonText}
              </div>
              <div className="absolute top-[0px] left-[0px] w-[17px] h-[17px]">
                {addIconSrc ? (
                  <img
                    className="w-[17px] h-[17px]"
                    width={17}
                    height={17}
                    alt="Add icon"
                    src={addIconSrc}
                  />
                ) : (
                  defaultAddIcon
                )}
              </div>
            </div>
          </button>

          <button
            onClick={onProfileClick}
            className="hover:opacity-80 transition-opacity"
          >
            <img
              className="w-[29px] relative h-[29px] object-cover rounded-full"
              width={29}
              height={29}
              alt="Profile"
              src={profileImageSrc}
            />
          </button>
        </div>
      </div>
    );
  }
);

Header.displayName = "Header";

export { Header };
