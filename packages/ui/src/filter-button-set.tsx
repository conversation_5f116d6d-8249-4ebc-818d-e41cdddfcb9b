"use client";

import * as React from "react";
import { Settings } from "lucide-react";
import { cn } from "./lib/utils";

export interface FilterButtonSetProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Whether the filter is active/selected */
  isActive?: boolean;
  /** Custom filter icon */
  filterIcon?: React.ReactNode;
  /** Callback when filter button is clicked */
  onFilterClick?: () => void;
  /** Show both active and inactive states for demo */
  showBothStates?: boolean;
}

const FilterButtonSet = React.forwardRef<HTMLDivElement, FilterButtonSetProps>(
  ({
    className,
    isActive = false,
    filterIcon,
    onFilterClick,
    showBothStates = false,
    ...props
  }, ref) => {
    const [internalActive, setInternalActive] = React.useState(isActive);

    const handleFilterClick = () => {
      if (!showBothStates) {
        setInternalActive(!internalActive);
      }
      onFilterClick?.();
    };

    const defaultFilterIcon = filterIcon || (
      <Settings className="w-5 h-5" />
    );

    if (showBothStates) {
      return (
        <div
          ref={ref}
          className={cn(
            "w-full relative rounded-[var(--radius-8)] border-dashed border-[1px] border-[#9747ff] box-border h-[120px] overflow-hidden",
            className
          )}
          {...props}
        >
          {/* Inactive state */}
          <button
            onClick={handleFilterClick}
            className="absolute top-[var(--spacing-20)] left-[var(--spacing-20)] rounded-[var(--radius-8)] bg-[var(--color-background-secondary)] w-[30px] h-[30px] flex flex-row items-center justify-center text-[var(--color-text-placeholder)] hover:opacity-80 transition-opacity"
          >
            {defaultFilterIcon}
          </button>
          
          {/* Active state */}
          <button
            onClick={handleFilterClick}
            className="absolute top-[70px] left-[var(--spacing-20)] rounded-[var(--radius-8)] bg-[var(--color-text-secondary)] w-[30px] h-[30px] flex flex-row items-center justify-center text-[var(--color-text-inverse)] hover:opacity-80 transition-opacity"
          >
            {defaultFilterIcon}
          </button>
        </div>
      );
    }

    return (
      <div ref={ref} className={className} {...props}>
        <button
          onClick={handleFilterClick}
          className={cn(
            "rounded-[var(--radius-8)] w-[30px] h-[30px] flex flex-row items-center justify-center transition-all duration-200 hover:opacity-80",
            internalActive
              ? "bg-[var(--color-text-secondary)] text-[var(--color-text-inverse)]"
              : "bg-[var(--color-background-secondary)] text-[var(--color-text-placeholder)]"
          )}
        >
          {defaultFilterIcon}
        </button>
      </div>
    );
  }
);

FilterButtonSet.displayName = "FilterButtonSet";

export { FilterButtonSet };
