"use client";

import * as React from "react";
import { DiscoverTuneIcon } from "./icons";
import { cn } from "./lib/utils";
import { type FilterOption } from "./filter-pills";

export interface FilterDropdownProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Available filter options */
  options?: FilterOption[];
  /** Currently selected filter options */
  selectedOptions?: FilterOption[];
  /** Callback when filter options change */
  onOptionsChange?: (options: FilterOption[]) => void;
  /** Callback when filter button is clicked */
  onFilterClick?: () => void;
  /** Whether the dropdown is open */
  isOpen?: boolean;
  /** Callback when dropdown open state changes */
  onOpenChange?: (open: boolean) => void;
}

const FilterDropdown = React.forwardRef<HTMLDivElement, FilterDropdownProps>(
  ({
    className,
    options = [],
    selectedOptions = [],
    onOptionsChange,
    onFilterClick,
    isOpen = false,
    onOpenChange,
    ...props
  }, ref) => {
    const [internalOpen, setInternalOpen] = React.useState(isOpen);
    const dropdownRef = React.useRef<HTMLDivElement>(null);

    // Handle click outside to close dropdown
    React.useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
          setInternalOpen(false);
          onOpenChange?.(false);
        }
      };

      if (internalOpen) {
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
      }
    }, [internalOpen, onOpenChange]);

    const handleFilterClick = () => {
      const newOpen = !internalOpen;
      setInternalOpen(newOpen);
      onOpenChange?.(newOpen);
      onFilterClick?.();
    };

    const handleOptionClick = (option: FilterOption) => {
      const isSelected = selectedOptions.some(selected => selected.id === option.id);
      let newSelectedOptions: FilterOption[];
      
      if (isSelected) {
        newSelectedOptions = selectedOptions.filter(selected => selected.id !== option.id);
      } else {
        newSelectedOptions = [...selectedOptions, option];
      }
      
      onOptionsChange?.(newSelectedOptions);
    };

    const removeOption = (optionId: string) => {
      const newSelectedOptions = selectedOptions.filter(option => option.id !== optionId);
      onOptionsChange?.(newSelectedOptions);
    };

    const isActive = selectedOptions.length > 0;

    return (
      <div ref={ref} className={cn("relative", className)} {...props}>
        <div ref={dropdownRef}>
          {/* Filter Button */}
          <button
            onClick={handleFilterClick}
            className={cn(
              "rounded-[var(--radius-8)] w-[30px] h-[30px] flex flex-row items-center justify-center transition-all duration-200 hover:opacity-80",
              isActive
                ? "bg-[var(--color-text-secondary)] text-[var(--color-text-inverse)]"
                : "bg-[var(--color-background-secondary)] text-[var(--color-text-placeholder)]"
            )}
          >
            <DiscoverTuneIcon size={20} />
          </button>

          {/* Dropdown Menu */}
          {internalOpen && options.length > 0 && (
            <div className="absolute top-[calc(100%+4px)] right-0 z-50 min-w-[200px] bg-[var(--color-background-primary)] border border-[var(--color-stroke)] rounded-[var(--radius-8)] shadow-lg overflow-hidden">
              {options.map((option) => {
                const isSelected = selectedOptions.some(selected => selected.id === option.id);
                return (
                  <button
                    key={option.id}
                    onClick={() => handleOptionClick(option)}
                    className={cn(
                      "w-full px-3 py-2 text-left text-sm hover:bg-[var(--color-background-secondary)] transition-colors",
                      isSelected
                        ? "bg-[var(--color-background-secondary)] text-[var(--color-text-primary)] font-medium"
                        : "text-[var(--color-text-primary)]"
                    )}
                  >
                    {option.label}
                  </button>
                );
              })}
            </div>
          )}
        </div>


      </div>
    );
  }
);

FilterDropdown.displayName = "FilterDropdown";

export { FilterDropdown };
