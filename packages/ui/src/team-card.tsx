"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface TeamCardProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Employee name */
  name?: string;
  /** Employee role/position */
  role?: string;
  /** Employee avatar image source */
  avatar?: string;
  /** Whether the card is in active/selected state */
  isActive?: boolean;
}

const TeamCard = React.forwardRef<HTMLDivElement, TeamCardProps>(
  ({
    className,
    name = "<PERSON>",
    role = "Diseña<PERSON>",
    avatar,
    isActive = false,
    ...props
  }, ref) => {
    // Component-specific variables from global design tokens
    const teamCardBorderColor = 'var(--color-stroke)';
    const teamCardBackgroundColor = isActive ? 'var(--color-background-secondary)' : 'var(--color-background-primary)';
    const teamCardNameColor = 'var(--color-text-primary)';
    const teamCardRoleColor = 'var(--color-text-secondary)';

    return (
      <div
        ref={ref}
        className={cn(
          "w-full relative rounded-[var(--radius-8)] border border-solid overflow-hidden flex flex-col items-start justify-start p-3 cursor-pointer hover:opacity-80 transition-opacity",
          className
        )}
        style={{
          backgroundColor: teamCardBackgroundColor,
          borderColor: teamCardBorderColor
        }}
        {...props}
      >
        {/* Circular Avatar */}
        <div className="w-[72px] h-[72px] rounded-full overflow-hidden bg-gray-200 flex items-center justify-center mb-[10px] flex-shrink-0">
          {avatar ? (
            <img
              src={avatar}
              alt={name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gray-300 flex items-center justify-center text-gray-600 text-sm font-medium">
              {name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'MM'}
            </div>
          )}
        </div>

        {/* Text Content Container */}
        <div className="flex flex-col w-full">
          {/* Employee Name */}
          <div
            className="font-inter font-semibold text-base leading-tight mb-[2px] overflow-hidden text-ellipsis whitespace-nowrap"
            style={{
              color: teamCardNameColor
            }}
          >
            {name}
          </div>

          {/* Employee Role */}
          <div
            className="font-roboto text-xs font-normal leading-tight overflow-hidden text-ellipsis whitespace-nowrap"
            style={{
              color: teamCardRoleColor
            }}
          >
            {role}
          </div>
        </div>
      </div>
    );
  }
);

TeamCard.displayName = "TeamCard";

export { TeamCard };
