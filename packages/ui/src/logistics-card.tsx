import * as React from "react";
import { cn } from "./lib/utils";

export type LogisticsStatus = "available" | "en_route";

export interface LogisticsCardProps extends React.HTMLAttributes<HTMLDivElement> {
  vehicleName: string;
  dateStart: string;
  dateEnd: string;
  status: LogisticsStatus;
  statusText: string;
  imageUrl: string;
  onCardClick?: () => void;
  imageClassName?: string;
}

export const LogisticsCard = React.forwardRef<HTMLDivElement, LogisticsCardProps>(
  (
    {
      className,
      vehicleName,
      dateStart,
      dateEnd,
      status,
      statusText,
      imageUrl,
      onCardClick,
      imageClassName,
      ...props
    },
    ref
  ) => {
    return (
      <div
        ref={ref}
        onClick={onCardClick}
        className={cn(
          "logistics-card border border-[var(--color-stroke)] rounded-[var(--radius-8)] bg-[var(--color-background-primary)] p-[12px] flex flex-col justify-between min-w-[260px] min-h-[220px] cursor-pointer hover:opacity-80 transition-opacity",
          className
        )}
        {...props}
      >
        {/* Top: Name and Dates */}
        <div className="logistics-card__header flex flex-col gap-1 mb-2">
          <div className="logistics-card__name font-inter font-semibold text-lg text-[var(--color-text-primary)]">
            {vehicleName}
          </div>
          <div className="logistics-card__dates text-[var(--color-text-secondary)] text-sm flex items-center gap-2">
            <span>{dateStart}</span>
            <span className="text-[var(--color-stroke)]">→</span>
            <span>{dateEnd}</span>
          </div>
        </div>
        {/* Status */}
        <div className="logistics-card__status flex items-center gap-2 mb-2">
          <span
            className={
              cn(
                "logistics-card__dot inline-block w-2.5 h-2.5 rounded-full",
                status === "available" ? "bg-[var(--color-green)]" : "bg-[var(--color-red)]"
              )
            }
          />
          <span
            className={
              status === "available"
                ? "logistics-card__status-text text-[var(--color-green)]"
                : "logistics-card__status-text text-[var(--color-red)]"
            }
          >
            {statusText}
          </span>
        </div>
        {/* Vehicle Image */}
        <div className="logistics-card__image flex-1 flex items-end justify-end">
          <img
            src={imageUrl}
            alt={vehicleName}
            className={cn("logistics-card__img object-contain select-none pointer-events-none", imageClassName ? imageClassName : "max-h-36")}
            draggable={false}
          />
        </div>
      </div>
    );
  }
);

LogisticsCard.displayName = "LogisticsCard"; 