"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface CatalogItemProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Product name */
  productName?: string;
  /** Product description */
  productDescription?: string;
  /** Product category label */
  categoryLabel?: string;
  /** Product image source */
  imageSrc?: string;
  /** Whether the item is in active/selected state */
  isActive?: boolean;
}

const CatalogItem = React.forwardRef<HTMLDivElement, CatalogItemProps>(
  ({
    className,
    productName = "Lámina Acero",
    productDescription = "4x8 | Calibre 12 | Inoxidable",
    categoryLabel = "Producto",
    imageSrc,
    isActive = false,
    ...props
  }, ref) => {
    // Component-specific variables from global design tokens
    const catalogItemBorderColor = '#e7e7e7';
    const catalogItemBackgroundColor = isActive ? '#f7f7f7' : '#fff'; // Secondary when active, primary when inactive
    const catalogItemImageBg = isActive ? '#fff' : '#ededed'; // Primary when active, secondary when inactive
    const catalogItemImagePlaceholderBg = '#5d5d5d';
    const catalogItemCategoryBg = isActive ? '#fff' : '#f7f7f7'; // Primary when active, secondary when inactive
    const catalogItemCategoryTextColor = '#5f6368';
    const catalogItemTitleColor = '#33393f';
    const catalogItemDescriptionColor = '#5f6368';

    return (
      <div
        ref={ref}
        className={cn(
          "w-full relative rounded-lg box-border overflow-hidden flex flex-col items-start justify-start p-3 gap-2 text-left text-[10px] font-inter border border-solid",
          className
        )}
        style={{
          backgroundColor: catalogItemBackgroundColor,
          borderColor: catalogItemBorderColor
        }}
        {...props}
      >
        <div className="self-stretch flex flex-row items-start justify-between gap-0">
          <div 
            className="w-[45px] rounded-sm h-[45px] flex flex-row items-center justify-center py-[3px] px-1 box-border"
            style={{ backgroundColor: catalogItemImageBg }}
          >
            {imageSrc ? (
              <img
                src={imageSrc}
                alt={productName}
                className="w-[17px] h-[29px] object-cover"
              />
            ) : (
              <div 
                className="w-[17px] h-[29px]"
                style={{ backgroundColor: catalogItemImagePlaceholderBg }}
              />
            )}
          </div>
          <div 
            className="rounded flex flex-row items-center justify-center py-0.5 px-2"
            style={{ backgroundColor: catalogItemCategoryBg }}
          >
            <div 
              className="relative font-semibold"
              style={{ color: catalogItemCategoryTextColor }}
            >
              {categoryLabel}
            </div>
          </div>
        </div>
        <div className="self-stretch flex flex-col items-start justify-start gap-1 text-sm">
          <div 
            className="relative font-semibold"
            style={{ color: catalogItemTitleColor }}
          >
            {productName}
          </div>
          <div 
            className="self-stretch relative text-[10px] overflow-hidden text-ellipsis whitespace-nowrap"
            style={{ color: catalogItemDescriptionColor }}
          >
            {productDescription}
          </div>
        </div>
      </div>
    );
  }
);

CatalogItem.displayName = "CatalogItem";

export { CatalogItem };
