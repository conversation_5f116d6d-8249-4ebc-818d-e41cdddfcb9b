import type { Meta, StoryObj } from '@storybook/react';
import { InventoryCard } from './inventory-card';

const meta: Meta<typeof InventoryCard> = {
  title: 'UI/InventoryCard',
  component: InventoryCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    name: {
      control: 'text',
      description: 'Name of the inventory item',
    },
    description: {
      control: 'text',
      description: 'Description of the inventory item',
    },
    quantity: {
      control: 'number',
      description: 'Current quantity',
    },
    unit: {
      control: 'text',
      description: 'Unit of measurement',
    },
    imageUrl: {
      control: 'text',
      description: 'Optional image URL',
    },
    onCardClick: {
      action: 'card-clicked',
      description: 'Click handler',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: 'Lámina Acero',
    description: '4×8 | Calibre 12 | Inoxidable',
    quantity: 21,
    unit: 'piezas',
  },
};

export const WithLongName: Story = {
  args: {
    name: 'Lámina de Acero Inoxidable Grado Industrial',
    description: '4×8 pies | Calibre 12 | Resistente a corrosión',
    quantity: 15,
    unit: 'piezas',
  },
};

export const WithoutUnit: Story = {
  args: {
    name: 'Soldadura MIG',
    description: 'Electrodo 6013 - 3.2mm x 350mm',
    quantity: 25,
  },
};

export const LowQuantity: Story = {
  args: {
    name: 'Disco de Corte',
    description: '9" x 1/8" | Para metal | Alta durabilidad',
    quantity: 3,
    unit: 'piezas',
  },
};

export const HighQuantity: Story = {
  args: {
    name: 'Tornillos M6',
    description: 'Hexagonales | 20mm | Acero galvanizado',
    quantity: 1250,
    unit: 'piezas',
  },
};

export const WithImage: Story = {
  args: {
    name: 'Tubo Cuadrado',
    description: '2"×2" | Calibre 14 | Estructural',
    quantity: 150,
    unit: 'metros',
    imageUrl: 'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=100&h=100&fit=crop',
  },
};

export const Interactive: Story = {
  render: () => {
    const handleClick = () => {
      alert('Inventory card clicked!');
    };

    return (
      <div className="w-[400px] space-y-4">
        <h3 className="text-lg font-semibold">Inventory Items</h3>
        <InventoryCard
          name="Lámina Acero A36"
          description="4×8 pies | Calibre 12 | Para construcción"
          quantity={25}
          unit="piezas"
          onCardClick={handleClick}
        />
        <InventoryCard
          name="Electrodo 6013"
          description="3.2mm x 350mm | Para soldadura general"
          quantity={15}
          unit="kg"
          onCardClick={handleClick}
        />
        <InventoryCard
          name="Casco de Soldadura"
          description="Careta fotosensible | Protección UV"
          quantity={8}
          unit="unidades"
          onCardClick={handleClick}
        />
      </div>
    );
  },
};

export const Grid: Story = {
  render: () => (
    <div className="grid grid-cols-1 gap-3 w-[400px]">
      <InventoryCard
        name="Lámina Acero"
        description="4×8 | Calibre 12 | Inoxidable"
        quantity={21}
        unit="piezas"
      />
      <InventoryCard
        name="Tubo Cuadrado"
        description="2"×2" | Calibre 14 | Estructural"
        quantity={150}
        unit="metros"
      />
      <InventoryCard
        name="Soldadura MIG"
        description="Electrodo 6013 - 3.2mm"
        quantity={25}
        unit="kg"
      />
      <InventoryCard
        name="Disco de Corte"
        description="9" x 1/8" | Para metal"
        quantity={45}
        unit="piezas"
      />
    </div>
  ),
};
